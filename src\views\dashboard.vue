<template>
    <div>
          <el-row :gutter="20" class="mgb20">
              <el-col :span="10">
                <el-card shadow="hover" :body-style="{ height: '400px' }">
                    <div class="card-header">
                        <p class="card-header-title">渠道统计</p>
                        <p class="card-header-desc">最近一个月的订单来源统计</p>
                    </div>
                    <v-chart class="map-chart" :option="mapOptions" @click="handleMapClick" />
                </el-card>
            </el-col>
            <el-col :span="9">
                <el-card shadow="hover" :body-style="{ height: '400px' }">
                    <div class="card-header">
                        <p class="card-header-title">时间线</p>
                        <p class="card-header-desc">{{ selectedRoute ? `${selectedRoute}线路订单趋势` : '请点击地图连接线查看趋势' }}</p>
                    </div>
                    <v-chart class="timeline-chart" :option="timelineOptions" />
                </el-card>
            </el-col>
          
            <el-col :span="5">
                <el-card shadow="hover" :body-style="{ height: '400px' }">
                    <div class="card-header">
                        <p class="card-header-title">实时时钟</p>
                        <p class="card-header-desc">当前系统时间</p>
                    </div>
                    <div class="clock-container">
                        <div class="clock">
                            <div class="clock-face">
                                <!-- 时钟刻度 -->
                                <div class="hour-numbers">
                                    <div class="hour-12">12</div>
                                    <div class="hour-1">1</div>
                                    <div class="hour-2">2</div>
                                    <div class="hour-3">3</div>
                                    <div class="hour-4">4</div>
                                    <div class="hour-5">5</div>
                                    <div class="hour-6">6</div>
                                    <div class="hour-7">7</div>
                                    <div class="hour-8">8</div>
                                    <div class="hour-9">9</div>
                                    <div class="hour-10">10</div>
                                    <div class="hour-11">11</div>
                                </div>



                                <!-- 时针 -->
                                <div
                                    class="hand hour-hand"
                                    :style="{ transform: `rotate(${hourAngle}deg)` }"
                                ></div>

                                <!-- 分针 -->
                                <div
                                    class="hand minute-hand"
                                    :style="{ transform: `rotate(${minuteAngle}deg)` }"
                                ></div>

                                <!-- 秒针 -->
                                <div
                                    class="hand second-hand"
                                    :style="{ transform: `rotate(${secondAngle}deg)` }"
                                ></div>

                                <!-- 中心点 -->
                                <div class="center-dot"></div>
                            </div>
                        </div>

                        <!-- 数字时间显示 -->
                        <div class="digital-time">
                            {{ digitalTime }}
                        </div>

                        <!-- 日期显示 -->
                        <div class="date-display">
                            {{ dateDisplay }}
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        <el-row :gutter="20" class="mgb20">
            <el-col :span="8">
                <el-card shadow="hover" :body-style="{ height: '200px' }">
                    <div class="card-header">
                        <p class="card-header-title">链路可用性统计</p>
                        <p class="card-header-desc">当前网络链路状态分布</p>
                    </div>
                    <div class="availability-container">
                        <div class="availability-item available">
                            <div class="availability-number">{{ linkAvailability.available }}</div>
                            <div class="availability-label">可用</div>
                        </div>
                        <div class="availability-item unavailable">
                            <div class="availability-number">{{ linkAvailability.unavailable }}</div>
                            <div class="availability-label">不可用</div>
                        </div>
                        <div class="availability-item unknown">
                            <div class="availability-number">{{ linkAvailability.unknown }}</div>
                            <div class="availability-label">未知</div>
                        </div>
                        <div class="availability-item total">
                            <div class="availability-number">{{ linkAvailability.total }}</div>
                            <div class="availability-label">合计</div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card shadow="hover" :body-style="{ height: '200px' }">
                    <div class="card-header">
                        <p class="card-header-title">全网可用性</p>
                        <p class="card-header-desc">整体网络运行状态</p>
                    </div>
                    <div class="network-availability">
                        <div class="network-status-circle" :class="networkStatus.class">
                            <div class="network-percentage">{{ networkStatus.percentage }}%</div>
                            <div class="network-status-text">{{ networkStatus.status }}</div>
                        </div>
                        <div class="network-details">
                            <div class="detail-item">
                                <span class="detail-label">总链路数：</span>
                                <span class="detail-value">{{ networkStatus.totalLinks }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">可用链路：</span>
                                <span class="detail-value">{{ networkStatus.availableLinks }}</span>
                            </div>
                        </div>
                    </div>
                </el-card>
            </el-col>
            <el-col :span="8">
                <el-card shadow="hover" :body-style="{ height: '200px' }">
                    <div class="card-header">
                        <p class="card-header-title">全网链路合规</p>
                        <p class="card-header-desc">链路合规性检查结果</p>
                    </div>
                    <div class="compliance-container">
                        <div class="compliance-chart">
                            <div class="compliance-circle">
                                <div class="compliance-percentage">{{ complianceData.percentage }}%</div>
                                <div class="compliance-status">{{ complianceData.status }}</div>
                            </div>
                            <svg class="compliance-progress" width="120" height="120">
                                <circle
                                    cx="60"
                                    cy="60"
                                    r="50"
                                    fill="none"
                                    stroke="#e6e6e6"
                                    stroke-width="8"
                                />
                                <circle
                                    cx="60"
                                    cy="60"
                                    r="50"
                                    fill="none"
                                    :stroke="complianceData.color"
                                    stroke-width="8"
                                    stroke-linecap="round"
                                    :stroke-dasharray="circumference"
                                    :stroke-dashoffset="strokeDashoffset"
                                    transform="rotate(-90 60 60)"
                                />
                            </svg>
                        </div>
                        <div class="compliance-details">
                            <div class="detail-item">
                                <span class="detail-label">合规链路：</span>
                                <span class="detail-value">{{ complianceData.compliantLinks }}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">总链路数：</span>
                                <span class="detail-value">{{ complianceData.totalLinks }}</span>
                            </div>
                        </div>
                    </div>
                </el-card>
            </el-col>
        </el-row>
        <el-row :gutter="20" class="mgb20">
            <el-col :span="18">
                <el-card shadow="hover">
                    <div class="card-header">
                        <p class="card-header-title">订单动态</p>
                        <p class="card-header-desc">最近一周订单状态，包括订单成交量和订单退货量</p>
                    </div>
                    <v-chart class="chart" :option="dashOpt1" />
                </el-card>
            </el-col>
            <el-col :span="6">
                <el-card shadow="hover">
                    <div class="card-header">
                        <p class="card-header-title">品类分布</p>
                        <p class="card-header-desc">最近一个月销售商品的品类情况</p>
                    </div>
                    <v-chart class="chart" :option="dashOpt2" />
                </el-card>
            </el-col>
        </el-row>
      
    </div>
</template>

<script setup lang="ts" name="dashboard">
import countup from '@/components/countup.vue';
import { use, registerMap } from 'echarts/core';
import { BarChart, LineChart, PieChart, MapChart, ScatterChart, LinesChart } from 'echarts/charts';
import {
    GridComponent,
    TooltipComponent,
    LegendComponent,
    TitleComponent,
    VisualMapComponent,
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import VChart from 'vue-echarts';
import { dashOpt1, dashOpt2, mapOptions } from './chart/options';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import chinaMap from '@/utils/china';
use([
    CanvasRenderer,
    BarChart,
    GridComponent,
    LineChart,
    PieChart,
    TooltipComponent,
    LegendComponent,
    TitleComponent,
    VisualMapComponent,
    MapChart,
    ScatterChart,
    LinesChart,
]);
registerMap('china', chinaMap);

// 响应式变量
const selectedRoute = ref('');

// 链路可用性数据
const linkAvailability = ref({
    available: 31,
    unavailable: 0,
    unknown: 0,
    total: 31
});

// 全网可用性数据
const networkStatus = ref({
    percentage: 99.3,
    status: '优秀',
    class: 'excellent',
    totalLinks: 404,
    availableLinks: 401
});

// 合规性数据
const complianceData = ref({
    percentage: 99.3,
    status: '合规',
    color: '#52c41a',
    compliantLinks: 401,
    totalLinks: 404
});

// 计算圆形进度条的周长和偏移
const circumference = computed(() => 2 * Math.PI * 50);
const strokeDashoffset = computed(() => {
    const progress = complianceData.value.percentage / 100;
    return circumference.value * (1 - progress);
});

// 不同线路的订单数据
const routeData = {
    '北京-上海': {
        dates: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        orders: [120, 150, 180, 200, 220, 250, 280],
        color: '#ff6600'
    },
    '上海-深圳': {
        dates: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        orders: [80, 95, 110, 130, 145, 160, 175],
        color: '#00bcd4'
    },
    '深圳-成都': {
        dates: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        orders: [60, 70, 85, 95, 110, 125, 140],
        color: '#1ABC9C'
    },
    '成都-西安': {
        dates: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        orders: [40, 50, 60, 70, 80, 90, 100],
        color: '#3f51b5'
    },
    '西安-北京': {
        dates: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
        orders: [70, 85, 100, 115, 130, 145, 160],
        color: '#f44336'
    }
};

// 时间线图表配置
const timelineOptions = computed(() => {
    const currentData = selectedRoute.value ? routeData[selectedRoute.value] : null;

    if (!currentData) {
        return {
            title: {
                text: '请点击地图连接线查看趋势',
                left: 'center',
                top: 'middle',
                textStyle: {
                    color: '#999',
                    fontSize: 16
                }
            },
            grid: { show: false },
            xAxis: { show: false },
            yAxis: { show: false },
            series: []
        };
    }

    return {
        tooltip: {
            trigger: 'axis',
            formatter: '{b}<br/>{a}: {c}单'
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: currentData.dates,
            axisLine: {
                lineStyle: { color: '#ddd' }
            },
            axisLabel: {
                color: '#666'
            }
        },
        yAxis: {
            type: 'value',
            name: '订单量',
            axisLine: {
                lineStyle: { color: '#ddd' }
            },
            axisLabel: {
                color: '#666'
            },
            splitLine: {
                lineStyle: { color: '#f0f0f0' }
            }
        },
        series: [
            {
                name: selectedRoute.value + ' 订单量',
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    color: currentData.color,
                    width: 3
                },
                itemStyle: {
                    color: currentData.color
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0, y: 0, x2: 0, y2: 1,
                        colorStops: [
                            { offset: 0, color: currentData.color + '40' },
                            { offset: 1, color: currentData.color + '10' }
                        ]
                    }
                },
                data: currentData.orders
            }
        ]
    };
});

// 地图点击事件处理
const handleMapClick = (params: any) => {
    // 只处理连接线的点击事件
    if (params.seriesType === 'lines' && params.data && params.data.name) {
        selectedRoute.value = params.data.name;
    }
};

// 钟表相关的响应式数据
const currentTime = ref(new Date());
let timeInterval: number | null = null;

// 计算时针角度
const hourAngle = computed(() => {
    const hours = currentTime.value.getHours() % 12;
    const minutes = currentTime.value.getMinutes();
    return (hours * 30) + (minutes * 0.5) - 90; // -90度是为了让12点指向顶部
});

// 计算分针角度
const minuteAngle = computed(() => {
    const minutes = currentTime.value.getMinutes();
    const seconds = currentTime.value.getSeconds();
    return (minutes * 6) + (seconds * 0.1) - 90;
});

// 计算秒针角度
const secondAngle = computed(() => {
    const seconds = currentTime.value.getSeconds();
    return (seconds * 6) - 90;
});

// 数字时间显示
const digitalTime = computed(() => {
    return currentTime.value.toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
});

// 日期显示
const dateDisplay = computed(() => {
    return currentTime.value.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        weekday: 'long'
    });
});

// 更新时间的函数
const updateTime = () => {
    currentTime.value = new Date();
};

// 组件挂载时启动定时器
onMounted(() => {
    timeInterval = setInterval(updateTime, 1000);
});

// 组件卸载时清除定时器
onUnmounted(() => {
    if (timeInterval) {
        clearInterval(timeInterval);
    }
});
</script>

<style>
.card-body {
    display: flex;
    align-items: center;
    height: 100px;
    padding: 0;
}
</style>
<style scoped>
.card-content {
    flex: 1;
    text-align: center;
    font-size: 14px;
    color: #999;
    padding: 0 20px;
}

.card-num {
    font-size: 30px;
}

.card-icon {
    font-size: 50px;
    width: 100px;
    height: 100px;
    text-align: center;
    line-height: 100px;
    color: #fff;
}

.bg1 {
    background: #2d8cf0;
}

.bg2 {
    background: #64d572;
}

.bg3 {
    background: #f25e43;
}

.bg4 {
    background: #e9a745;
}

.color1 {
    color: #2d8cf0;
}

.color2 {
    color: #64d572;
}

.color3 {
    color: #f25e43;
}

.color4 {
    color: #e9a745;
}

.chart {
    width: 100%;
    height: 400px;
}

.card-header {
    padding-left: 10px;
    margin-bottom: 20px;
}

.card-header-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.card-header-desc {
    font-size: 14px;
    color: #999;
}

.timeline-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    color: #000;
}

.timeline-time,
.timeline-desc {
    font-size: 12px;
    color: #787878;
}

/* 钟表样式 */
.clock-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    padding: 20px 0;
}

.clock {
    position: relative;
    width: 200px;
    height: 200px;
    margin-bottom: 20px;
}

.clock-face {
    position: relative;
    width: 100%;
    height: 100%;
    border: 4px solid #333;
    border-radius: 50%;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    box-shadow:
        0 0 20px rgba(0,0,0,0.1),
        inset 0 0 20px rgba(255,255,255,0.8);
}



/* 时钟数字容器 */
.hour-numbers {
    position: absolute;
    width: 100%;
    height: 100%;
    font-weight: bold;
    font-size: 16px;
    color: #333;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
}

.hour-numbers > div {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

/* 各个时钟数字的位置 - 使用数学计算的精确圆形布局 */
/* 基于200px直径的圆，半径85px，数字距离边缘15px */
.hour-12 {
    position: absolute;
    top: 15px;
    left: 50%;
    transform: translateX(-50%);
}

.hour-1 {
    position: absolute;
    top: 27px;  /* sin(30°) * 85 + 15 */
    right: 27px; /* cos(30°) * 85 + 15 */
}

.hour-2 {
    position: absolute;
    top: 57px;  /* sin(60°) * 85 + 15 */
    right: 15px; /* cos(60°) * 85 + 15 */
}

.hour-3 {
    position: absolute;
    top: 50%;
    right: 15px;
    transform: translateY(-50%);
}

.hour-4 {
    position: absolute;
    bottom: 57px;
    right: 15px;
}

.hour-5 {
    position: absolute;
    bottom: 27px;
    right: 27px;
}

.hour-6 {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
}

.hour-7 {
    position: absolute;
    bottom: 27px;
    left: 27px;
}

.hour-8 {
    position: absolute;
    bottom: 57px;
    left: 15px;
}

.hour-9 {
    position: absolute;
    top: 50%;
    left: 15px;
    transform: translateY(-50%);
}

.hour-10 {
    position: absolute;
    top: 57px;
    left: 15px;
}

.hour-11 {
    position: absolute;
    top: 27px;
    left: 27px;
}

.hand {
    position: absolute;
    bottom: 50%;
    left: 50%;
    transform-origin: bottom center;
    border-radius: 2px;
}

.hour-hand {
    width: 4px;
    height: 50px;
    background: #333;
    margin-left: -2px;
    z-index: 3;
    transition: transform 0.5s ease-in-out;
}

.minute-hand {
    width: 3px;
    height: 70px;
    background: #666;
    margin-left: -1.5px;
    z-index: 2;
    transition: transform 0.3s ease-in-out;
}

.second-hand {
    width: 1px;
    height: 80px;
    background: #e74c3c;
    margin-left: -0.5px;
    z-index: 4;
    transition: transform 0.1s ease-in-out;
}

.center-dot {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    background: #333;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;
}

.digital-time {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    font-family: 'Courier New', monospace;
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.date-display {
    font-size: 14px;
    color: #666;
    text-align: center;
}
.map-chart {
    width: 100%;
    height: 350px;
}
.timeline-chart {
    width: 100%;
    height: 350px;
}

/* 链路可用性样式 */
.availability-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    height: 120px;
    padding: 10px;
}

.availability-item {
    text-align: center;
    padding: 10px;
    border-radius: 8px;
    min-width: 60px;
    transition: transform 0.3s ease;
}

.availability-item:hover {
    transform: translateY(-2px);
}

.availability-item.available {
    background: linear-gradient(135deg, #52c41a, #73d13d);
    color: white;
}

.availability-item.unavailable {
    background: linear-gradient(135deg, #ff4d4f, #ff7875);
    color: white;
}

.availability-item.unknown {
    background: linear-gradient(135deg, #8c8c8c, #bfbfbf);
    color: white;
}

.availability-item.total {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    color: white;
}

.availability-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.availability-label {
    font-size: 12px;
    opacity: 0.9;
}

/* 全网可用性样式 */
.network-availability {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 120px;
    padding: 10px;
}

.network-status-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.network-status-circle.excellent {
    background: linear-gradient(135deg, #52c41a, #73d13d);
    color: white;
}

.network-status-circle.good {
    background: linear-gradient(135deg, #1890ff, #40a9ff);
    color: white;
}

.network-status-circle.warning {
    background: linear-gradient(135deg, #faad14, #ffc53d);
    color: white;
}

.network-status-circle.danger {
    background: linear-gradient(135deg, #ff4d4f, #ff7875);
    color: white;
}

.network-percentage {
    font-size: 18px;
    font-weight: bold;
}

.network-status-text {
    font-size: 10px;
    opacity: 0.9;
}

.network-details {
    display: flex;
    gap: 15px;
    font-size: 12px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.detail-label {
    color: #666;
    margin-bottom: 2px;
}

.detail-value {
    font-weight: bold;
    color: #333;
}

/* 合规性样式 */
.compliance-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 120px;
    padding: 10px;
}

.compliance-chart {
    position: relative;
    margin-bottom: 10px;
}

.compliance-circle {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 2;
}

.compliance-percentage {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.compliance-status {
    font-size: 10px;
    color: #666;
}

.compliance-progress {
    transform: rotate(-90deg);
}

.compliance-details {
    display: flex;
    gap: 15px;
    font-size: 12px;
}
</style>
